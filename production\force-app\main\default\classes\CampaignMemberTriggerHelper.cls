/************************************* MODIFICATION LOG ********************************************************************************************
* CampaignMemberTriggerHelper
*
* DESCRIPTION : Helper for Campaign Member trigger
*
*---------------------------------------------------------------------------------------------------------------------------------------------------
* DEVELOPER                     DATE                                   REASON
*---------------------------------------------------------------------------------------------------------------------------------------------------
* Yaroslav Chuiev               07/22/2021                        -- Update Campaign Chatter Messages
*/

public without sharing class CampaignMemberTriggerHelper {

    public static final String MCS_CAMPAIGN_ID_METADATA_NAME = 'MCS_Trial_Campaign_Id';
    public static final String UNMAPPED_ACCCOUNT_ID_METADATA_NAME = 'Unmapped_Account_Id';


    /*public static final List<String> VALID_PART_OF_TITLES = new List<String>{
        'Director', 'Vice', 'Senior Vice', 'Chief', 'President', 'Founder', 'Senior Director',
        'General Manager', 'VP', 'SVP', 'Head Of'
    };

    public static final List<String> VALID_TITLES = new List<String>{
        'CTO', 'CEO', 'CFO', 'CAO', 'CRO', 'CHRO', 'CIO', 'CDO', 'CDAO'
    };

    public static final List<String> VALID_COUNTRIES = new List<String>{
        'USA', 'United States', 'Canada', 'CA', 'United Kingdom', 'UK', 'Netherlands', 'Singapore', 'Dubai', 'UAE', 'Australia',
        'New Zealand', 'Malaysia', 'Thailand', 'Indonesia', 'Philippines', 'England', 'Ireland', 'Gibraltar', 'Scotland',
        'Sweden', 'Norway', 'Denmark', 'Finland', 'India', 'Sri Lanka', 'Nepal', 'Pakistan', 'United Arab Emirates',
        'Bahrain', 'Qatar', 'Kuwait', 'Oman', 'South Africa'
    };

    public static final List<String> VALID_STATUSES = new List<String>{
        'Attended', 'Viewed On-Demand'
    };*/

    public static final Map<String, String> MEMBER_STATUS_BY_TACTIC = new Map<String, String>{
        'Content Syndication' => 'Downloaded',
        'Email Marketing' => 'Sent',
        'Inbound Call ' => 'Answer',
        'In-Person Event' => 'Invited',
        'Partner In-Person Event' => 'Registered',
        'Tradeshow' => 'Invited',
        'Virtual Roundtable' => 'Registered',
        '3rd Party Webinar' => 'Registered',
        'Owned Webinar' => 'Registered',
        'Partner Webinar' => 'Registered',
        'World' => 'Registered',
        'Sales' => 'Sent',
        'Organic Social' => '',
        'Paid Programmatic' => 'Downloaded',
        'Paid Search' => 'Downloaded',
        'Paid Social' => 'Downloaded',
        'Third Party Lead Gen' => 'Downloaded',
        'Print Advertising' => 'Downloaded',
        'Website' => 'Downloaded',
        'Direct Mail' => 'Sent',
        'OOH' => 'Viewed',
        'Education' => 'Sent',
        'OPERATIONAL - DO NOT USE' => '',
        'Community' => 'Sent',
        'App Store' => 'Downloaded',
        'ABM' => 'Sent'
    };

    public static final String GREETINGS_TEXT = 'Hi ';

    /*public static final String ACTION_FOR_US_CANADA = 'ACTION: The Inside Sales Team will be following up on this interaction within 24 hours and will update the {0} status as appropriate: \n' +
    //Placeholder for Lead/Contact Statuses
        '{1} \n' +
        'The Account Executive will be cc’d on communications. \n' +
        'https://mstr.my.salesforce.com/<ID> \n';
    public static final String ACTION_OUTSIDE_US_CANADA = 'ACTION: Please follow up with this interaction within 24 hours and update the {0} status as appropriate: \n' +
        //Placeholder for Lead/Contact Statuses
        '{1} \n' +
        'Please work with your BDR and/or AAE as you see fit. \n' +
        'https://mstr.my.salesforce.com/<ID> \n';*/


    public static void stampCampaignMemberStatus(List<CampaignMember> campaignMembers, Set<Id> campaignIds) {
        Map<Id, Campaign> campaignsByIds = new Map<Id, Campaign>([
            SELECT Id, Name, Type
            FROM Campaign
            WHERE Id IN :campaignIds
        ]);

        Campaign campaign;
        for (CampaignMember campaignMember : campaignMembers) {
            campaign = campaignsByIds.get(campaignMember.CampaignId);

            if (MEMBER_STATUS_BY_TACTIC.containsKey(campaign.Type)) {
                campaignMember.Status = MEMBER_STATUS_BY_TACTIC.get(campaign.Type);
            } else if (campaign.Type == 'Owned Webinar' &&
                (campaign.Name.containsIgnoreCase('on-demand') || campaign.Name.containsIgnoreCase('on demand') || campaign.Name.containsIgnoreCase('(od)'))
                ) {
                campaignMember.Status = 'Viewed On-Demand';
            }
        }
    }

    /*public static void sendChatterNotification(Set<Id> campaignMemberIds) {
        Map<Id, List<CampaignMember>> campaignMembersByContactId = new Map<Id, List<CampaignMember>>();
        Map<Id, List<CampaignMember>> campaignMembersByLeadId = new Map<Id, List<CampaignMember>>();

        List<CampaignMember> campaignMembers = [
            SELECT Id, Campaign.Type, Campaign.Post_To_Chatter__c, Status,
                ContactId, Contact.Title, LeadId, Lead.Title, CampaignId, Campaign.Name,
                Campaign.RecordType.Name, Customer_Status__c, Campaign.AccountCategoryFilter__c, Campaign.UserTitleFilter__c
            FROM CampaignMember
            WHERE Id IN :campaignMemberIds AND Campaign.Post_To_Chatter__c = TRUE AND (
                (NOT Campaign.Type IN ('4. Webinar', '8. CXO Event', '10. Local Event', '11. Partner Event')) OR
                (Campaign.Type IN ('8. CXO Event', '10. Local Event', '11. Partner Event') AND Status = 'Attended') OR
                (Campaign.Type = '4. Webinar' AND Status IN ('Attended', 'Viewed On-Demand'))
            )
        ];

        for (CampaignMember campaignMember : campaignMembers) {
            List<String> accountStatuses = String.isNotBlank(campaignMember.Campaign.AccountCategoryFilter__c) ? campaignMember.Campaign.AccountCategoryFilter__c.split(';') : new List<String>();
            if (accountStatuses.isEmpty() || accountStatuses.contains('All') || accountStatuses.contains(campaignMember.Customer_Status__c) ||
                (accountStatuses.size() == 1 && accountStatuses.contains('Empty') && String.isBlank(campaignMember.Customer_Status__c))
                ) {
                String title = String.isNotBlank(campaignMember.Contact.Title) ? campaignMember.Contact.Title : (String.isNotBlank(campaignMember.Lead.Title) ? campaignMember.Lead.Title : '');
                Boolean skipTitleCheck = String.isBlank(campaignMember.Campaign.UserTitleFilter__c) || campaignMember.Campaign.UserTitleFilter__c == 'All';
                Boolean considerIt = false;

                if (!skipTitleCheck && String.isNotBlank(title) && campaignMember.Campaign.UserTitleFilter__c == 'Director and above') {
                    if (VALID_TITLES.contains(title)) {
                        considerIt = true;
                    }

                    for (String validPart : VALID_PART_OF_TITLES) {
                        if (title.contains(validPart)) {
                            considerIt = true;
                        }
                    }
                }

                if (skipTitleCheck || considerIt) {
                    if (campaignMember.ContactId != null) {
                        if (campaignMembersByContactId.containsKey(campaignMember.ContactId)) {
                            campaignMembersByContactId.get(campaignMember.ContactId).add(campaignMember);
                        } else {
                            campaignMembersByContactId.put(campaignMember.ContactId, new List<CampaignMember>{
                                campaignMember
                            });
                        }
                    }

                    if (campaignMember.LeadId != null) {
                        if (campaignMembersByLeadId.containsKey(campaignMember.LeadId)) {
                            campaignMembersByLeadId.get(campaignMember.LeadId).add(campaignMember);
                        } else {
                            campaignMembersByLeadId.put(campaignMember.LeadId, new List<CampaignMember>{
                                campaignMember
                            });
                        }
                    }
                }
            }
        }

        if (!campaignMembersByContactId.isEmpty() || !campaignMembersByLeadId.isEmpty()) {
            processChatterNotifications(campaignMembersByContactId, campaignMembersByLeadId);
        }
    }*/
    
    public static void UpdateCampaignMemberFields(List<CampaignMember> campaignMembersToUpdateFields) {
        
        for (campaignMember cmpMbr : campaignMembersToUpdateFields) {
            cmpMbr.TrialStartDate__c = system.now();
            cmpMbr.TrialEndDate__c = cmpMbr.TrialStartDate__c.addDays(30);
        }
    }
    
    @future (callout=true)
    public static void calloutToPushCampaignMemberFieldsUpdate(String jsonBody) {
        try {
            Chat_Bot_API__mdt chatBotAPI= Chat_Bot_API__mdt.getInstance('Config');
            
            Http http = new Http();
            HttpRequest request = new HttpRequest();
            if (TriggersHelper.runningInASandbox)
                request.setEndpoint('callout:ChatBotAPISandbox/'+chatBotAPI.Trial_URL_Path__c);
            else
                request.setEndpoint('callout:ChatBotAPIProduction/'+chatBotAPI.Trial_URL_Path__c);
            request.setHeader('x-mstr-key',chatBotAPI.Trial_X_MSTR_Key__c);
            request.setHeader('Content-Type', 'application/json; charset=UTF-8');
            request.setHeader('Accept', 'application/json');
            request.setBody(jsonBody);
            request.setMethod('POST');
            HttpResponse response = http.send(request);
            system.debug('Status Code>>'+response.getStatusCode());
        } catch(Exception ex) {
            system.debug('Exception>>'+ex.getMessage());
        }
    }
    
    public static void insertOpportunities(List<CampaignMember> campaignMembersToInsertOpportunities, Map<Id,Contact> mapOfConIdVsContact, Map<Id,Lead> mapOfLeadIdVsLead, List<Opportunity> opportunitiesToCreate) {
        Map<Id,Id> mapOfLeadIdVsConId = new Map<Id,Id>();
        if (!mapOfLeadIdVsLead.isEmpty()) {
            List<Database.LeadConvert> leadsConverts = new List<Database.LeadConvert>();
            for (Lead ld : mapOfLeadIdVsLead.values()) {
                Database.LeadConvert leadConvert = new database.LeadConvert();
                leadConvert.setLeadId(ld.Id);
                leadConvert.setDoNotCreateOpportunity(true);
                leadConvert.setAccountId(ld.Account_2__c);
                leadConvert.convertedStatus = 'Converted';
                leadsConverts.add(leadConvert);
            }
            for (Database.LeadConvertResult lcr : Database.convertLead(leadsConverts))
                mapOfLeadIdVsConId.put(lcr.getLeadId(),lcr.getContactId());
        }
        
        List<String> pickListValues = new List<String>();
        Schema.DescribeFieldResult fieldResult = Schema.getGlobalDescribe().get('Opportunity').getDescribe().fields.getMap().get('RegionPricebook__c').getDescribe();
        for( Schema.PicklistEntry pickList : fieldResult.getPicklistValues())
            pickListValues.add(pickList.getValue());
        for (campaignMember cmpMbr : campaignMembersToInsertOpportunities) {
            if (mapOfConIdVsContact.containsKey(cmpMbr.ContactId)) {
                Contact con = mapOfConIdVsContact.get(cmpMbr.ContactId);
                Opportunity oppObj = new Opportunity();
                oppObj.Name =  (con.Account.Name+' – Auto-Express Upgrade – ').left(120);
                oppObj.Business_Executive__c = cmpMbr.ContactId;
                oppObj.AccountId = con.AccountId;
                oppObj.OwnerId = con.Account.OwnerId;
                if (pickListValues.contains(con.Account.BillingCountry+' - '+con.Account.CurrencyIsoCode))
                    oppObj.RegionPricebook__c = con.Account.BillingCountry+' - '+con.Account.CurrencyIsoCode;
                oppObj.CloseDate = system.today().addDays(30);
                oppObj.CampaignId = cmpMbr.CampaignId;
                oppObj.Internal_Comments_Long__c = cmpMbr.Notes__c != null?cmpMbr.Notes__c+' Trial Start Date '+cmpMbr.TrialStartDate__c:
                'Trial Start Date '+String.valueOf(cmpMbr.TrialStartDate__c);
                oppObj.RecordTypeId = Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Sales_Opportunity_New').getRecordTypeId();
                oppObj.StageName = 'S1 - Recognize Needs';
                opportunitiesToCreate.add(oppObj);
            } 
            else if (mapOfLeadIdVsLead.containsKey(cmpMbr.LeadId)) {
                Opportunity oppObj = new Opportunity();
                Lead ld = mapOfLeadIdVsLead.get(cmpMbr.LeadId);
                oppObj.Name =  (ld.Account_2__r.Name+' – Auto-Express Upgrade – ').left(120);
                oppObj.Business_Executive__c = mapOfLeadIdVsConId.get(ld.Id);
                oppObj.AccountId = ld.Account_2__c;
                oppObj.OwnerId = ld.Account_2__r.OwnerId;
                if (pickListValues.contains(ld.Account_2__r.BillingCountry+' - '+ld.Account_2__r.CurrencyIsoCode))
                    oppObj.RegionPricebook__c = ld.Account_2__r.BillingCountry+' - '+ld.Account_2__r.CurrencyIsoCode;
                oppObj.CloseDate = system.today().addDays(30);
                oppObj.CampaignId = cmpMbr.CampaignId;
                oppObj.Internal_Comments_Long__c = cmpMbr.Notes__c != null?cmpMbr.Notes__c+' Trial Start Date '+cmpMbr.TrialStartDate__c:
                'Trial Start Date '+String.valueOf(cmpMbr.TrialStartDate__c);
                oppObj.RecordTypeId = Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get('Sales_Opportunity_New').getRecordTypeId();
                oppObj.StageName = 'S1 - Recognize Needs';
                opportunitiesToCreate.add(oppObj);
            }
        }
    }

    public static void processChatterNotifications(List<InputVariables> InputVariablesList) {
        List<ConnectApi.BatchInput> inputs = new List<ConnectApi.BatchInput>();
        List<CollaborationGroup> groupList = [SELECT Id FROM CollaborationGroup WHERE Name = 'MQL Notifications'];
      
        for (InputVariables InputVariable : InputVariablesList) {
            //People tagged in CC on the bottom
            Set<String> ccList = new Set<String>{InputVariable.managerId};
            ccList.remove(null);
            ccList.remove('');
            inputs.add(generateFeedInput(InputVariable, new List<String>(ccList), groupList));
        }
        if (!inputs.isEmpty() && !Test.isRunningTest()){
            ConnectApi.ChatterFeeds.postFeedElementBatch(Network.getNetworkId(), inputs);
        }
    }

    private static ConnectAPI.BatchInput generateFeedInput(InputVariables InputVariable, List<String> ccList, List<CollaborationGroup> groupList) {
        Id recordId = (Id)InputVariable.leadContactId;
        Schema.SObjectType sobjectType = recordId.getSObjectType();
        String sobjectName = sobjectType.getDescribe().getName();
        
        ConnectApi.FeedItemInput feedItemInput = new ConnectApi.FeedItemInput();
        ConnectApi.MessageBodyInput messageBodyInput = new ConnectApi.MessageBodyInput();
        messageBodyInput.messageSegments = new List<ConnectApi.MessageSegmentInput>();
        
        ConnectApi.MarkupBeginSegmentInput paragraphBeginInput = new ConnectApi.MarkupBeginSegmentInput();
        paragraphBeginInput.markupType = ConnectApi.MarkupType.Paragraph;
        
        ConnectApi.MarkupBeginSegmentInput boldBeginInput = new ConnectApi.MarkupBeginSegmentInput();
        boldBeginInput.markupType = ConnectApi.MarkupType.Bold;
        
        ConnectApi.TextSegmentInput lineBreakInput = new ConnectApi.TextSegmentInput();
        lineBreakInput.text =  '&nbsp;';
        
        ConnectApi.MarkupEndSegmentInput paragraphEndInput = new ConnectApi.MarkupEndSegmentInput();
        paragraphEndInput.markupType = ConnectApi.MarkupType.Paragraph;
        
        ConnectApi.MarkupEndSegmentInput boldEndInput = new ConnectApi.MarkupEndSegmentInput();
        boldEndInput.markupType = ConnectApi.MarkupType.Bold;
        
        ConnectApi.TextSegmentInput textSegmentGreetings = new ConnectApi.TextSegmentInput();
        textSegmentGreetings.text = GREETINGS_TEXT;
        messageBodyInput.messageSegments.add(textSegmentGreetings);
        
        ConnectApi.MentionSegmentInput mentionInput = new ConnectApi.MentionSegmentInput();
        mentionInput.id = InputVariable.ownerId;
        messageBodyInput.messageSegments.add(mentionInput);
        
        ConnectApi.TextSegmentInput textSegmentInput1 = new ConnectApi.TextSegmentInput();
        InputVariable.status = InputVariable.status == 'L3 - Engaged' ? 'MQL' : InputVariable.status;
        textSegmentInput1.text = ',';
        messageBodyInput.messageSegments.add(textSegmentInput1); 
        messageBodyInput.messageSegments.add(paragraphBeginInput);
        messageBodyInput.messageSegments.add(lineBreakInput);
        messageBodyInput.messageSegments.add(paragraphEndInput);
        
        ConnectApi.TextSegmentInput textSegmentInput2 = new ConnectApi.TextSegmentInput();
        
        ConnectApi.MarkupBeginSegmentInput leadContactUrlBeginInput = new ConnectApi.MarkupBeginSegmentInput();
        leadContactUrlBeginInput.markupType = ConnectApi.MarkupType.Hyperlink;
        leadContactUrlBeginInput.url = InputVariable.leadContactUrl;
        leadContactUrlBeginInput.altText = InputVariable.name;
        messageBodyInput.messageSegments.add(leadContactUrlBeginInput);
        
        ConnectApi.TextSegmentInput leadContactUrlSegmentInput = new ConnectApi.TextSegmentInput();
        leadContactUrlSegmentInput.text = InputVariable.name;
        messageBodyInput.messageSegments.add(leadContactUrlSegmentInput);
        
        ConnectApi.MarkupEndSegmentInput leadContactUrlEndInput = new ConnectApi.MarkupEndSegmentInput();
        leadContactUrlEndInput.markupType = ConnectApi.MarkupType.Hyperlink;
        messageBodyInput.messageSegments.add(leadContactUrlEndInput);
        
        if (InputVariable.status == 'TQL') {
            textSegmentInput2.text = ', '+(!String.isBlank(InputVariable.title)? (InputVariable.title+' at '): ' ')+InputVariable.accName+' is a new TQL (Tele-Marketing Qualified Lead - i.e., a Lead that a BDR has qualified for an AE or CSM) assigned to you for next steps. '+
                'You will receive a write-up detailing the previous marketing engagement, qualifying conversations as well as a Salesforce Meeting that can be found under the "Activities" tab on this '+sobjectName+' record.';
        }
        else {
        	textSegmentInput2.text = ', '+(!String.isBlank(InputVariable.title)? (InputVariable.title+' at '): ' ')+InputVariable.accName+' is a new MQL (Marketing Qualified Lead) assigned to you.';
        }
        messageBodyInput.messageSegments.add(textSegmentInput2);
        messageBodyInput.messageSegments.add(paragraphBeginInput);
        messageBodyInput.messageSegments.add(lineBreakInput);
        messageBodyInput.messageSegments.add(paragraphEndInput);
        
        ConnectApi.TextSegmentInput textSegmentInput3 = new ConnectApi.TextSegmentInput();
        textSegmentInput3.text = 'Your next steps and responsibilities:';
        if (InputVariable.status == 'TQL')
        messageBodyInput.messageSegments.add(boldBeginInput);
        messageBodyInput.messageSegments.add(textSegmentInput3);
        if (InputVariable.status == 'TQL')
        messageBodyInput.messageSegments.add(boldEndInput);
        messageBodyInput.messageSegments.add(paragraphBeginInput);
        ConnectApi.TextSegmentInput textSegmentInput4 = new ConnectApi.TextSegmentInput();
        if (InputVariable.status == 'TQL')
            textSegmentInput4.text = '1. If you accept the Meeting, move the '+sobjectName+' Status from TQL to SAL (Sales Accepted Lead)';
        else
            textSegmentInput4.text = '1. Research campaign history and formulate a follow up plan';  
        messageBodyInput.messageSegments.add(textSegmentInput4);
        messageBodyInput.messageSegments.add(paragraphEndInput);
        messageBodyInput.messageSegments.add(paragraphBeginInput);
        ConnectApi.TextSegmentInput textSegmentInput5 = new ConnectApi.TextSegmentInput();
        if (InputVariable.status == 'TQL')
            textSegmentInput5.text = '2. Work with the meeting creator to confirm the meeting and appropriate hand off process';
        else
            textSegmentInput5.text = '2. Begin outreach via Phone, Email, and LinkedIn';  
        messageBodyInput.messageSegments.add(textSegmentInput5);
        messageBodyInput.messageSegments.add(paragraphEndInput);
        messageBodyInput.messageSegments.add(paragraphBeginInput);
        ConnectApi.TextSegmentInput textSegmentInput6 = new ConnectApi.TextSegmentInput();
        if (InputVariable.status == 'TQL')
            textSegmentInput6.text = '3. Update the Salesforce meeting status, conclusion, and next steps post-meeting; this is important for pipeline hygiene and feedback into the Marketing engine';
        else
            textSegmentInput6.text = '3. Maintain appropriate '+sobjectName+' Status and Dispositions (link for more details is below)';  
        messageBodyInput.messageSegments.add(textSegmentInput6);
        messageBodyInput.messageSegments.add(paragraphEndInput);
        
        messageBodyInput.messageSegments.add(paragraphBeginInput);
        messageBodyInput.messageSegments.add(lineBreakInput);
        messageBodyInput.messageSegments.add(paragraphEndInput);
        
        if (InputVariable.status == 'MQL') {
            messageBodyInput.messageSegments.add(paragraphBeginInput);
            ConnectApi.TextSegmentInput textSegmentInput7 = new ConnectApi.TextSegmentInput();
            textSegmentInput7.text = 'MQL Service Level Agreement';
            messageBodyInput.messageSegments.add(boldBeginInput);
            messageBodyInput.messageSegments.add(textSegmentInput7);
            messageBodyInput.messageSegments.add(boldEndInput);
            messageBodyInput.messageSegments.add(paragraphEndInput);
            messageBodyInput.messageSegments.add(paragraphBeginInput);
            ConnectApi.TextSegmentInput textSegmentInput8= new ConnectApi.TextSegmentInput();
            textSegmentInput8.text = '1. Follow up is to begin within 24 business hours of the MQL assignment';
            messageBodyInput.messageSegments.add(textSegmentInput8);
            messageBodyInput.messageSegments.add(paragraphEndInput);
            
            messageBodyInput.messageSegments.add(paragraphBeginInput);
            ConnectApi.TextSegmentInput textSegmentInput9= new ConnectApi.TextSegmentInput();
            textSegmentInput9.text = '2. All activities should be logged in Salesforce on the '+sobjectName+' record';
            messageBodyInput.messageSegments.add(textSegmentInput9);
            messageBodyInput.messageSegments.add(paragraphEndInput);
            
            messageBodyInput.messageSegments.add(paragraphBeginInput);
            ConnectApi.TextSegmentInput textSegmentInput10= new ConnectApi.TextSegmentInput();
            textSegmentInput10.text = '3. All '+sobjectName+'s will either become Opportunities in Salesforce, be routed to Marketing for a Nurture Campaign, or Disqualified';
            messageBodyInput.messageSegments.add(textSegmentInput10);
            messageBodyInput.messageSegments.add(paragraphEndInput);
            
            messageBodyInput.messageSegments.add(paragraphBeginInput);
            messageBodyInput.messageSegments.add(lineBreakInput);
            messageBodyInput.messageSegments.add(paragraphEndInput);
        }
        
        ConnectApi.TextSegmentInput textSegmentInput11= new ConnectApi.TextSegmentInput();
        textSegmentInput11.text = 'Detailed information on our '+sobjectName+' Process, Status Definitions, and Disposition Options can be found ';
        messageBodyInput.messageSegments.add(textSegmentInput11);
        
        ConnectApi.MarkupBeginSegmentInput linkBeginInput = new ConnectApi.MarkupBeginSegmentInput();
        linkBeginInput.markupType = ConnectApi.MarkupType.Hyperlink;
        linkBeginInput.url = 'https://microstrategy-my.sharepoint.com/:p:/p/rrogers/ETSLAfXHABpJl0VD26I_2CYBGGOxDazDTH7TptWM4Q5UWw?e=0wFFs4';
        linkBeginInput.altText = 'HERE';
        messageBodyInput.messageSegments.add(linkBeginInput);

        ConnectApi.TextSegmentInput linkSegmentInput = new ConnectApi.TextSegmentInput();
        linkSegmentInput.text = 'HERE';
        messageBodyInput.messageSegments.add(linkSegmentInput);

        ConnectApi.MarkupEndSegmentInput linkEndInput = new ConnectApi.MarkupEndSegmentInput();
        linkEndInput.markupType = ConnectApi.MarkupType.Hyperlink;
        messageBodyInput.messageSegments.add(linkEndInput);
        
        ConnectApi.TextSegmentInput textSegmentInput12= new ConnectApi.TextSegmentInput();
        textSegmentInput12.text = '. For questions, please reach out to your manager.';
        messageBodyInput.messageSegments.add(textSegmentInput12);
        
        messageBodyInput.messageSegments.add(paragraphBeginInput);
        messageBodyInput.messageSegments.add(lineBreakInput);
        messageBodyInput.messageSegments.add(paragraphEndInput);
        
        ConnectApi.TextSegmentInput textSegmentCC = new ConnectApi.TextSegmentInput();
        textSegmentCC.text = 'CC: ';
        messageBodyInput.messageSegments.add(textSegmentCC);
        
        //Create list for people in CC
        for (Integer i = 0; i < ccList.size(); i++) {
            ConnectApi.MentionSegmentInput ccInput = new ConnectApi.MentionSegmentInput();
            ccInput.id = ccList.get(i);
            messageBodyInput.messageSegments.add(ccInput);
            
            if (i != ccList.size() - 1) {
                ConnectApi.TextSegmentInput separatorSegment = new ConnectApi.TextSegmentInput();
                separatorSegment.text = ', ';
                messageBodyInput.messageSegments.add(separatorSegment);
            }
        }
        
        if (!ccList.isEmpty()) {
            ConnectApi.TextSegmentInput textSegmentLast = new ConnectApi.TextSegmentInput();
            textSegmentLast.text = ', ';
            messageBodyInput.messageSegments.add(textSegmentLast);
        } 
        
        for (CollaborationGroup grp : groupList) {
            ConnectApi.MentionSegmentInput mentionInput2 = new ConnectApi.MentionSegmentInput();
            mentionInput2.id = grp.Id;
            messageBodyInput.messageSegments.add(mentionInput2);
        }
        
        feedItemInput.subjectId = InputVariable.leadContactId;
        feedItemInput.body = messageBodyInput;
        feedItemInput.feedElementType = ConnectApi.FeedElementType.FeedItem;
        
        return new ConnectApi.BatchInput(feedItemInput);
    }

    public static void processMQLTQLChatterNotifications(List<SObject> persons, Set<Id> newOwners) {
        Map<Id, User> ownerUsers = new Map<Id, User>();
        Map<Id,SObject> relatedFields = new Map<Id,SObject>();
        Set<Id> contactIds = new Set<Id>();
        Set<Id> leadIds = new Set<Id>();
        
        for (sObject person : persons) {
            switch on person.getSObjectType().getDescribe().getName() {
                when 'Contact' {
                    Contact personContact = (Contact) person;
                    if(personContact.AccountId != null && personContact.AccountId != TriggersHelper.unmappedAccountId && personContact.AccountId != TriggersHelper.resourceCentreAccountId){
                            contactIds.add(personContact.Id);
                    }
                }
                when 'Lead' {
                    Lead personLead = (Lead) person;
                    if(personLead.Account_2__c != null && personLead.Account_2__c != TriggersHelper.unmappedAccountId && personLead.Account_2__c != TriggersHelper.resourceCentreAccountId){
                            leadIds.add(personLead.Id);
                    }
                }
            }
        }
        if(!newOwners.isEmpty()){
            ownerUsers = new Map<Id, User>([SELECT Id, ManagerId FROM User WHERE Id IN: newOwners]);
            if(!contactIds.isEmpty()){
                    relatedFields.putAll([SELECT Id, Account.Name, Name FROM Contact WHERE Id IN: contactIds]);
            }
            if(!leadIds.isEmpty()){
                    relatedFields.putAll([SELECT Id, Account_2__r.Name, Name FROM Lead WHERE Id IN: leadIds]);
            }
            List<CampaignMemberTriggerHelper.InputVariables> InputVariablesList = new List<CampaignMemberTriggerHelper.InputVariables>();
            for (sObject person : persons) {
                InputVariablesList = new List<CampaignMemberTriggerHelper.InputVariables>();
                CampaignMemberTriggerHelper.InputVariables InputVariables = new CampaignMemberTriggerHelper.InputVariables();
                switch on person.getSObjectType().getDescribe().getName() {
                    when 'Contact' {
                        Contact personContact = (Contact) person;
                        if(!contactIds.contains(personContact.Id)) continue;
                        InputVariables.ownerId = personContact.OwnerId;
                        InputVariables.managerId = ownerUsers.keySet().contains(personContact.OwnerId)? ownerUsers.get(personContact.OwnerId).ManagerId : null;
                        InputVariables.name = personContact.Name != null? personContact.Name: relatedFields.get(personContact.Id) != null? ((Contact) relatedFields.get(personContact.Id)).Name : null;
                        InputVariables.accName = personContact.Account.Name != null? personContact.Account.Name: relatedFields.get(personContact.Id) != null? ((Contact) relatedFields.get(personContact.Id)).Account.Name : null;
                        if(String.isBlank(InputVariables.accName)) continue;
                        InputVariables.title = personContact.Title;
                        InputVariables.status = personContact.L_Status__c;
                        InputVariables.leadContactId = personContact.Id;
                        InputVariables.leadContactUrl = URL.getOrgDomainUrl().toExternalForm()+'/'+personContact.Id;
                        InputVariablesList.add(InputVariables);
                    }
                    when 'Lead' {
                        Lead personLead = (Lead) person;
                        if(!leadIds.contains(personLead.Id)) continue;
                        InputVariables.ownerId = personLead.OwnerId;
                        InputVariables.managerId = ownerUsers.keySet().contains(personLead.OwnerId)? ownerUsers.get(personLead.OwnerId).ManagerId : null;
                        InputVariables.name = personLead.Name != null? personLead.Name: relatedFields.get(personLead.Id) != null? ((Lead) relatedFields.get(personLead.Id)).Name : null;
                        InputVariables.accName = personLead.Account_2__r.Name != null? personLead.Account_2__r.Name: relatedFields.get(personLead.Id) != null? ((Lead) relatedFields.get(personLead.Id)).Account_2__r.Name : null;
                        if(String.isBlank(InputVariables.accName)) continue;
                        InputVariables.title = personLead.Title;
                        InputVariables.status = personLead.Status;
                        InputVariables.leadContactId = personLead.Id;
                        InputVariables.leadContactUrl = URL.getOrgDomainUrl().toExternalForm()+'/'+personLead.Id;
                        InputVariablesList.add(InputVariables);
                        }
                }
                }
            processChatterNotifications(InputVariablesList);
        }
    }
    
    /*private static String generateWHO(String name, String title) {
        return 'WHO : ' + name + ', ' + title + '\n';
    }

    private static String generateWHERE(String accName) {
        return 'WHERE : ' + accName + '\n';
    }

    private static String generateWHAT(List<CampaignMember> campaignMembers) {
        String whatName = 'WHAT : ';
        for (CampaignMember campaignMember : campaignMembers) {
            whatName += campaignMember.Campaign.Name + ', ';
        }
        return whatName.removeEnd(', ') + '\n\n';
    }

    private static String generateAction(Boolean contactCountryUSCanada, Id contactLeadId, List<String> statusPicklistValues) {
        String action = contactCountryUSCanada ? ACTION_FOR_US_CANADA : ACTION_OUTSIDE_US_CANADA;
        action = String.format(action.replaceAll('<ID>', contactLeadId), statusPicklistValues);

        return action;
    }

    private static List<String> getArgumentsForActionText(String objectName,SObjectField field) {
        List<String> pickListValuesList = new List<String>();

        for (Schema.PicklistEntry pickListVal : field.getDescribe().getPicklistValues()) {
            pickListValuesList.add(pickListVal.getLabel());
        }
        return new List<String>{
            objectName,
            String.join(pickListValuesList, ', ')
        };
    }

    public static void setCampaignChatterPost(List<CampaignMember> campaignMembersForChatterPost) {
        for (CampaignMember cm : campaignMembersForChatterPost) {
            cm.Post_To_Chatter__c = True;
        }
    }*/

    /**
     * @description Processes MCS Campaign Members to set Trial Dates and Notes.
     * @param mcsCampaignMembers List of CampaignMember records to process.
     * @param leadDetailsMap Map of Lead Ids to Lead records, containing details for notes.
     *                       (e.g., Partner Account Name, form responses if stored on Lead).
     */
    public static void processMCSCampaignMemberUpdates(List<CampaignMember> mcsCampaignMembers, Map<Id, Lead> leadDetailsMap) {
        for (CampaignMember cm : mcsCampaignMembers) {
            // 1. Stamp Trial Start & Trial End Date
            // "Date of Entering Campaign" for a new CM is effectively its creation date.
            // Using Date.today() as a reliable value during the transaction.
            cm.TrialStartDate__c = Date.today(); 
            cm.TrialEndDate__c = cm.TrialStartDate__c.addDays(30);

            // 2. Concatenate additional information into CampaignMemberNotes__c
            // "If a campaign members comes through the website on a partner specific page, 
            //  the Campaign Member notes are stamped with the relevant Partner's account name. 
            //  This partner account should be the first item reflected in the notes. 
            //  The second should be the form questions & responses."
            
            String partnerAccountName = '';
            String formQuestionsResponses = ''; // This could be from Lead.Description or other custom fields

            // Retrieve partner name and form Q&A. This example assumes they might be on the Lead.
            // Adjust field names (e.g., Partner_Account_Name_On_Lead__c, Form_Responses_On_Lead__c) as per your actual setup.
            if (cm.LeadId != null && leadDetailsMap != null && leadDetailsMap.containsKey(cm.LeadId)) {
                Lead l = leadDetailsMap.get(cm.LeadId);
                
                // Example: Assume Partner Account Name is on a custom field on Lead
                // if (l.Partner_Account_Name_Source__c != null) {
                //     partnerAccountName = l.Partner_Account_Name_Source__c; 
                // }
                
                // Example: Assume Form Questions/Responses are in Lead's Description
                // if (String.isNotBlank(l.Description)) {
                //    formQuestionsResponses = l.Description;
                // }

                // FOR DEMONSTRATION - Replace with actual field access
                // This is a placeholder. You need to define where Partner Account Name and Form Q&A come from.
                // E.g., if Lead has a field `Web_Form_Partner_Account__c`
                // if(String.isNotBlank(l.get('Web_Form_Partner_Account__c'))) {
                //    partnerAccountName = (String)l.get('Web_Form_Partner_Account__c');
                // }
                // if(String.isNotBlank(l.Description)) { // Or another field for Q&A
                //    formQuestionsResponses = l.Description;
                // }
            } else if (cm.ContactId != null) {
                // If it's a Contact, similar logic might be needed, potentially querying the Contact
                // or assuming this info is passed to CampaignMember directly.
            }

            String concatenatedNotes = '';
            if (String.isNotBlank(partnerAccountName)) {
                concatenatedNotes += partnerAccountName;
            }

            if (String.isNotBlank(formQuestionsResponses)) {
                if (String.isNotBlank(concatenatedNotes)) {
                    concatenatedNotes += '\n'; // Separator
                }
                concatenatedNotes += formQuestionsResponses;
            }
            
            // If there are notes to stamp, set them.
            // Consider if you need to append to existing cm.Notes__c or overwrite.
            // The requirement "concatenated and filled out" suggests setting it.
            if (String.isNotBlank(concatenatedNotes)) {
                cm.Notes__c = concatenatedNotes;
            } else if (cm.Notes__c == null) {
                cm.Notes__c = '';
            }
        }
    }

    
    public class InputVariables{
        
        @InvocableVariable
        public Id ownerId;
        
        @InvocableVariable
        public String managerId;
        
        @InvocableVariable
        public String name;
        
        @InvocableVariable
        public String accName;
        
        @InvocableVariable
        public String title;
        
        @InvocableVariable
        public String status;
        
        @InvocableVariable
        public String leadContactUrl;
        
        @InvocableVariable
        public String leadContactId;
    }
}