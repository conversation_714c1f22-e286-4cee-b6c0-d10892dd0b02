/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 25/05/2021.
 */

public without sharing class CampaignMemberTriggerExecutionController implements TriggerHandler {

    private Set<Id> leadsMostRecentAction;
    private Set<Id> contactsMostRecentAction;
    private List<CampaignMember> campaignMembersNew;
    private Set<Id> campaignIds;

    //private Set<Id> campaignMembersForChatterGrouping;
    private Set<Id> campaignIdsToCallout;
    //private Set<Id> campaignIdsForChatterPost;
    //private Map<Id, CampaignMember> campaignMembersForChatterPost;
    //private List<CampaignMember> leadsForChatterPost;
    private List<CampaignMember> campaignMembersToCallout;
    private Map<Id, campaignMember> oldCampaignMembersMapToCallout;
 
    public Integer getRecursionDepth () {
        return 0;
    }

    public Integer getMaxRecursionDepthAllowed () {
        return 0;
    }

    public Boolean isEnabled () {
        return true;
    }
    
    private static List<Id> trialCampaignIds {
        get {
            if (trialCampaignIds == null) {
                trialCampaignIds = [SELECT Value__c FROM MSTR_Global_Configuation__mdt WHERE DeveloperName = 'Trial_Campaign_Ids'].Value__c.split(',');
            }
            return trialCampaignIds;
        }
        set;
    }

    public void bulkBefore () {
        campaignIds = new Set<Id>();
        campaignMembersNew = new List<CampaignMember>();
        campaignIdsToCallout = new Set<Id>();
        //campaignIdsForChatterPost = new Set<Id>();
        //campaignMembersForChatterPost = new Map<Id,CampaignMember>();
        //leadsForChatterPost = new List<CampaignMember>();
        campaignMembersToCallout = new List<CampaignMember>();
    }

    public void bulkAfter () {
        leadsMostRecentAction = new Set<Id>();
        contactsMostRecentAction = new Set<Id>();
        campaignMembersToCallout = new List<CampaignMember>();
        campaignIdsToCallout = new Set<Id>();
        oldCampaignMembersMapToCallout = new Map<Id, campaignMember>();
        //campaignMembersForChatterGrouping = new Set<Id>();
    }

    public void beforeInsert (SObject so) {
        CampaignMember campaignMember = (CampaignMember) so;
        Boolean isLead = campaignMember.LeadId != null && campaignMember.ContactId == null;
        Boolean isContact = campaignMember.ContactId != null && campaignMember.LeadId == null;
        campaignMembersToCallout.add(campaignMember);
        campaignIdsToCallout.add(campaignMember.CampaignId);

        if (campaignMember.CampaignId != null) {
            campaignIds.add(campaignMember.CampaignId);
            campaignMembersNew.add(campaignMember);
        }
    
        /*if(!campaignMember.Post_To_Chatter__c && (isLead || isContact)){
            campaignIdsForChatterPost.add(campaignMember.CampaignId);
            campaignMembersForChatterPost.put(campaignMember.Id, campaignMember);
            if(isLead){
                leadsForChatterPost.add(campaignMember);
            }
        }*/
    }

    public void beforeUpdate (SObject oldSo, SObject newSo) {
    }

    public void beforeDelete (SObject so) {
    }

    public void afterInsert (SObject so) {
        CampaignMember campaignMember = (CampaignMember) so;

        if (campaignMember.LeadId != null && campaignMember.CampaignId != TriggersHelper.redReportCampaignId && !campaignMember.Marketo_Excluded__c) {
            CampaignMemberLeadController.checkSyncToMarketo(campaignMember.LeadId);
            leadsMostRecentAction.add(campaignMember.LeadId);
        }

        if (campaignMember.ContactId != null && campaignMember.CampaignId != TriggersHelper.redReportCampaignId && !campaignMember.Marketo_Excluded__c) {
            CampaignMemberContactController.checkSyncToMarketo(campaignMember.ContactId);
            contactsMostRecentAction.add(campaignMember.ContactId);
        }

        /*if (campaignMember.Post_to_Chatter__c) {
            campaignMembersForChatterGrouping.add(campaignMember.Id);
        }*/
    }

    public void afterUpdate (SObject oldSo, SObject newSo) {
        CampaignMember oldCampaignMember = (CampaignMember) oldSo;
        CampaignMember newCampaignMember = (CampaignMember) newSo;
        If ((TriggersHelper.valueChangedNotBlank(oldCampaignMember, newCampaignMember, CampaignMember.TrialEndDate__c) && newCampaignMember.Email != null)
            || ((newCampaignMember.ContactId != null || newCampaignMember.LeadId != null) && newCampaignMember.TrialUpgradeRequest__c && TriggersHelper.valueChanged(oldCampaignMember, newCampaignMember, CampaignMember.TrialUpgradeRequest__c))){
                campaignIdsToCallout.add(newCampaignMember.CampaignId);
                campaignMembersToCallout.add(newCampaignMember);
                oldCampaignMembersMapToCallout.put(oldCampaignMember.Id,oldCampaignMember);
            }
        /*if (newCampaignMember.Post_to_Chatter__c) {
            if (!CampaignMemberTriggerHelper.VALID_STATUSES.contains(oldCampaignMember.Status) && CampaignMemberTriggerHelper.VALID_STATUSES.contains(newCampaignMember.Status) ||
                oldCampaignMember.Post_to_Chatter__c != newCampaignMember.Post_to_Chatter__c
            ) {
                campaignMembersForChatterGrouping.add(newCampaignMember.Id);

            }
        }*/
    }

    public void afterDelete (SObject so) {
        CampaignMember campaignMember = (CampaignMember) so;

        if (campaignMember.LeadId != null) {
            leadsMostRecentAction.add(campaignMember.LeadId);
        }

        if (campaignMember.ContactId != null) {
            contactsMostRecentAction.add(campaignMember.ContactId);
        }
    }

    public void afterUndelete (SObject so) {
    }

    public void andFinally () {
        List<Opportunity> opportunitiesToCreate = new List<Opportunity>();
        List<CampaignMemberFieldInputs> CampaignMemberFieldInputsToCallout = new List<CampaignMemberFieldInputs>();
        Map<Id,Contact> mapOfConIdVsContact = new Map<Id,Contact>();
        Map<Id,Lead> mapOfLeadIdVsLead = new Map<Id,Lead>();
        Set<Id> conIds = new Set<Id>();
        Set<Id> LeadIds = new Set<Id>();
        List<CampaignMember> campaignMembersToInsertOpportunities = new List<CampaignMember>();
        List<CampaignMember> campaignMembersToUpdateFields = new List<CampaignMember>();
        Campaign cmp;
        Map<Id,Campaign> queriedCampaigns = new Map<Id,Campaign>();
        Map<Id,Lead> queriedLeads = new Map<Id,Lead>();
        String campaignFields = ''; 
        String campaignFilter = ''; 
        String leadFields = ''; 
        String leadFilter = ''; 

        if (campaignIdsToCallout != null && !campaignIdsToCallout.isEmpty()) {
            campaignFields += 'Id,ParentId'; 
            campaignFilter += 'Id IN: campaignIdsToCallout'; 
        }
        /*if (campaignIdsForChatterPost != null && !campaignIdsForChatterPost.isEmpty()) {
            campaignFields += !String.isBlank(campaignFields)? ',': ''; 
            campaignFields += 'Post_To_Chatter__c '; 
            campaignFilter += !String.isBlank(campaignFilter)? ' OR ': ''; 
            campaignFilter += 'Id IN: campaignIdsForChatterPost'; 
        }*/
        if(!String.isBlank(campaignFields) && !String.isBlank(campaignFilter)){
            queriedCampaigns = new Map<Id,Campaign>((List<Campaign>)Database.query('SELECT ' + campaignFields + ' ' +
                'FROM Campaign ' +
                'WHERE ' + campaignFilter));
        }

        if (campaignIdsToCallout != null && !campaignIdsToCallout.isEmpty()) {
            for (Id cmpId : campaignIdsToCallout) {
                cmp = queriedCampaigns.get(cmpId);
                if (cmp != null && (trialCampaignIds.contains(cmp.Id) || trialCampaignIds.contains(cmp.ParentId) || Test.isRunningTest())) {
                    for (CampaignMember cmpMember : campaignMembersToCallout) {
                        if (cmpMember.CampaignId == cmp.Id) {
                            if (oldCampaignMembersMapToCallout == null) {
                                campaignMembersToUpdateFields.add(cmpMember);
                                if ((cmpMember.ContactId != null || cmpMember.LeadId != null) && cmpMember.TrialUpgradeRequest__c) {
                                    if (cmpMember.ContactId != null)
                                        conIds.add(cmpMember.ContactId);
                                    else if (cmpMember.LeadId != null)
                                        LeadIds.add(cmpMember.LeadId);
                                    campaignMembersToInsertOpportunities.add(cmpMember);
                                }
                            }
                            else {
                                if (TriggersHelper.valueChangedNotBlank(oldCampaignMembersMapToCallout.get(cmpMember.Id), cmpMember, CampaignMember.TrialEndDate__c) && cmpMember.Email != null) {
                                    CampaignMemberFieldInputs fieldInputs = new CampaignMemberFieldInputs();
                                    fieldInputs.end_date = String.valueOf(cmpMember.TrialEndDate__c);
                                    fieldInputs.email = cmpMember.Email;
                                    CampaignMemberFieldInputsToCallout.add(fieldInputs);
                                }
                                if ((cmpMember.ContactId != null || cmpMember.LeadId != null) && cmpMember.TrialUpgradeRequest__c && TriggersHelper.valueChanged(oldCampaignMembersMapToCallout.get(cmpMember.Id), cmpMember, CampaignMember.TrialUpgradeRequest__c)){
                                    if (cmpMember.ContactId != null)
                                        conIds.add(cmpMember.ContactId);
                                    else if (cmpMember.LeadId != null)
                                        LeadIds.add(cmpMember.LeadId);
                                    campaignMembersToInsertOpportunities.add(cmpMember);
                                }
                            }
                        }
                    }
                }
            }
        }
        /*if (campaignIdsForChatterPost != null && !campaignIdsForChatterPost.isEmpty()) {
            for (Id cmpId : campaignIdsForChatterPost) {
                cmp = queriedCampaigns.get(cmpId);
                if (cmp != null && !cmp.Post_To_Chatter__c) {
                    campaignMembersForChatterPost.remove(cmpId);
                }
            }
        }*/
        if (!conIds.isEmpty()) {
            String contactQuery = 'SELECT Id, Account.Name, Account.OwnerId, Account.BillingCountry, Account.CurrencyIsoCode FROM Contact WHERE Id IN: conIds AND AccountId != null';
            mapOfConIdVsContact = new Map<Id,Contact>((List<Contact>)Database.query(contactQuery));
        }
        
        if (LeadIds != null && !LeadIds.isEmpty()) {
            leadFields += 'Id, Account_2__c, Account_2__r.Name, Account_2__r.OwnerId, Account_2__r.BillingCountry, Account_2__r.CurrencyIsoCode'; 
            leadFilter += '(Id IN: LeadIds'; 
        }
        /*if (leadsForChatterPost != null && !leadsForChatterPost.isEmpty()) {
            Set<Id> lIds = new Set<Id>();
            for(CampaignMember cmL: leadsForChatterPost){
                lIds.add(cmL.LeadId);
            }
            leadFields += !String.isBlank(leadFields)? '': 'Id'; 
            leadFilter += !String.isBlank(leadFilter)? ' OR ': '('; 
            leadFilter += 'Id IN: lIds '; 
        }*/
        leadFilter += ') ' + 'AND Account_2__c != null'; 
        if(!String.isBlank(leadFields) && !String.isBlank(leadFilter)){
            queriedLeads = new Map<Id,Lead>((List<Lead>)Database.query('SELECT ' + leadFields + ' ' +
                'FROM Lead ' +
                'WHERE ' + leadFilter));
        }
        if (LeadIds != null && !LeadIds.isEmpty()) {
            for(Id leadId: LeadIds){
                mapOfLeadIdVsLead.put(leadId, queriedLeads.get(leadId));
            }
        }
        /*if (leadsForChatterPost != null && !leadsForChatterPost.isEmpty()) {
            for(CampaignMember lChP: leadsForChatterPost){
                if(queriedLeads.get(lChP.LeadId) == null){
                    campaignMembersForChatterPost.remove(lChP.Id);
                }
            }
        }
        if(campaignMembersForChatterPost != null && !campaignMembersForChatterPost.isEmpty()){
            CampaignMemberTriggerHelper.setCampaignChatterPost(campaignMembersForChatterPost.values());
        }*/

        List<CampaignMember> mcsCampaignMembersToUpdate = new List<CampaignMember>();
        
        if (Trigger.isInsert) { // Process only for newly inserted CampaignMembers
            String mcsCampaignId = null;
            MSTR_Global_Configuation__mdt mcsConfig = MSTR_Global_Configuation__mdt.getInstance(CampaignMemberTriggerHelper.MCS_CAMPAIGN_ID_METADATA_NAME);
            if (mcsConfig != null && String.isNotBlank(mcsConfig.Value__c)) {
                mcsCampaignId = mcsConfig.Value__c;
            } else {
                System.debug(LoggingLevel.WARN, 'MCS Trial Campaign ID not configured in MSTR_Global_Configuation__mdt.');
            }
            String unmappedAccountId = null;
            MSTR_Global_Configuation__mdt mcsConfigForUnmappedAccount = MSTR_Global_Configuation__mdt.getInstance(CampaignMemberTriggerHelper.UNMAPPED_ACCCOUNT_ID_METADATA_NAME);
            if (mcsConfigForUnmappedAccount != null && String.isNotBlank(mcsConfigForUnmappedAccount.Value__c)) {
                unmappedAccountId = mcsConfigForUnmappedAccount.Value__c;
            } else {
                System.debug(LoggingLevel.WARN, 'Unmapped Account ID not configured in MSTR_Global_Configuation__mdt.');
            }

            if (mcsCampaignId != null) {
                List<Id> mcsCM_IdsToQuery = new List<Id>();
                Set<Id> mcs_LeadIdsForDetailsQuery = new Set<Id>();

                for (CampaignMember cmFromTrigger : (List<CampaignMember>)Trigger.new) {
                    if (cmFromTrigger.CampaignId == mcsCampaignId) {
                        mcsCM_IdsToQuery.add(cmFromTrigger.Id);
                        if (cmFromTrigger.LeadId != null && cmFromTrigger.ContactId == null) {
                            mcs_LeadIdsForDetailsQuery.add(cmFromTrigger.LeadId);
                        }
                    }
                }
                System.debug('mcs_LeadIdsForDetailsQuery -> ' + mcs_LeadIdsForDetailsQuery);

                if (!mcsCM_IdsToQuery.isEmpty()) {
                    List<CampaignMember> mcsQueriedCampaignMembers = [
                        SELECT Id, CampaignId, LeadId, ContactId, Notes__c, TrialStartDate__c, TrialEndDate__c
                        FROM CampaignMember WHERE Id IN :mcsCM_IdsToQuery
                    ];

                    Map<Id, Lead> mcsLeadDetailsMap = new Map<Id, Lead>();
                    if (!mcs_LeadIdsForDetailsQuery.isEmpty()) {
                        mcsLeadDetailsMap = new Map<Id, Lead>([
                            SELECT Id, Company, Account_2__c
                            FROM Lead
                            WHERE Id IN :mcs_LeadIdsForDetailsQuery 
                        ]);
                    }
                    
                    List<CampaignMember> mcsCMsReadyForProcessing = new List<CampaignMember>();
                    for(CampaignMember cm : mcsQueriedCampaignMembers){
                        if(cm.LeadId != null && cm.ContactId == null){ // Is a lead
                            mcsCMsReadyForProcessing.add(cm);
                        } else { // Is a contact or already converted lead
                            mcsCMsReadyForProcessing.add(cm);
                        }
                    }

                    if (!mcsCMsReadyForProcessing.isEmpty()) {
                        // Call helper to set Trial Dates and Notes
                        CampaignMemberTriggerHelper.processMCSCampaignMemberUpdates(mcsCMsReadyForProcessing, mcsLeadDetailsMap);

                        // Lead Conversion for MCS members
                        List<Database.LeadConvert> leadsToConvert_MCS = new List<Database.LeadConvert>();
                        
                        for (CampaignMember cm : mcsCMsReadyForProcessing) {
                            if (cm.LeadId != null && cm.ContactId == null && mcsLeadDetailsMap.containsKey(cm.LeadId)) {
                                Lead leadDetail = mcsLeadDetailsMap.get(cm.LeadId);
                                Id acntID = null;
                                if(leadDetail.Account_2__c != null) {
                                    acntId = leadDetail.Account_2__c;
                                } else {
                                    acntId = unmappedAccountId;
                                }
                                
                                Database.LeadConvert lc = new Database.LeadConvert();
                                lc.setLeadId(leadDetail.Id);
                                lc.setConvertedStatus('Converted');
                                lc.setDoNotCreateOpportunity(true); // IMPORTANT: Do not create an Opportunity
                                lc.setAccountId(acntId);
                                leadsToConvert_MCS.add(lc);
                            }
                        }

                        if (!leadsToConvert_MCS.isEmpty()) {
                            List<Database.LeadConvertResult> lcResults_MCS = Database.convertLead(leadsToConvert_MCS, false);
                            for (Database.LeadConvertResult lcr : lcResults_MCS) {
                                if (lcr.isSuccess()) {
                                    // Update the CampaignMember with the new ContactId
                                    for (CampaignMember cm : mcsCMsReadyForProcessing) {
                                        if (cm.LeadId == lcr.getLeadId()) {
                                            cm.ContactId = lcr.getContactId();
                                            // cm.LeadId = null; // SF usually handles this post-conversion for the CM object in DB.
                                                              // The in-memory object might still have LeadId temporarily.
                                        }
                                    }
                                } else {
                                    System.debug(LoggingLevel.ERROR, 'MCS Lead Conversion Error for Lead ID ' + lcr.getLeadId() + ': ' + lcr.getErrors()[0].getMessage());
                                    // Potentially log these errors more formally
                                }
                            }
                        }
                        mcsCampaignMembersToUpdate.addAll(mcsCMsReadyForProcessing);
                    }
                }
            }
        }


        //Don't run default status rules for Marketo Integration user.
        if (UserInfo.getUserId() != TriggersHelper.marketoUserConfiguration.Value__c && campaignMembersNew != null && !campaignMembersNew.isEmpty()) {
            CampaignMemberTriggerHelper.stampCampaignMemberStatus(campaignMembersNew, campaignIds);
        }

        if (leadsMostRecentAction != null && !leadsMostRecentAction.isEmpty()) {
            CampaignMemberLeadController.stampCampaignFields(leadsMostRecentAction);
        }

        if (contactsMostRecentAction != null && !contactsMostRecentAction.isEmpty()) {
            CampaignMemberContactController.stampCampaignFields(contactsMostRecentAction);
        }

        /*if (campaignMembersForChatterGrouping != null && !campaignMembersForChatterGrouping.isEmpty()){
            CampaignMemberTriggerHelper.sendChatterNotification(campaignMembersForChatterGrouping);
        }*/

        CampaignMemberLeadController.updateLeads();
        CampaignMemberContactController.updateContacts();
        
        Map<Id, CampaignMember> finalCampaignMembersToUpdateMap = new Map<Id, CampaignMember>();
        // Add MCS CMs (already processed with dates, notes, and ContactId from conversion)
        for(CampaignMember cm : mcsCampaignMembersToUpdate) {
            finalCampaignMembersToUpdateMap.put(cm.Id, cm);
        }

        if (!finalCampaignMembersToUpdateMap.isEmpty()) {
            update finalCampaignMembersToUpdateMap.values();
        }

        //update CampaignMember's Fields
        if (!campaignMembersToUpdateFields.isEmpty()) {
            CampaignMemberTriggerHelper.UpdateCampaignMemberFields(campaignMembersToUpdateFields);
        }
        //insert Opportunities
        if (!campaignMembersToInsertOpportunities.isEmpty()) {
            CampaignMemberTriggerHelper.insertOpportunities(campaignMembersToInsertOpportunities, mapOfConIdVsContact, mapOfLeadIdVsLead, opportunitiesToCreate);
        }
        //update push CampaignMember's fields through callout
        if (!CampaignMemberFieldInputsToCallout.isEmpty()) {
            CampaignMemberTriggerHelper.calloutToPushCampaignMemberFieldsUpdate(JSON.serialize(CampaignMemberFieldInputsToCallout));
        }
        if (!opportunitiesToCreate.isEmpty()) 
            insert opportunitiesToCreate;
    }
    
    private class CampaignMemberFieldInputs {
        private String end_Date;
        private String email;
    }
}